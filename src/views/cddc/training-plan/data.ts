import dayjs, { type Dayjs } from 'dayjs';
import type { FormSchema } from '@geega-ui-plus/geega-ui';
import { unitTree } from '/@/api/admin/project';
import { userQuery } from '/@/api/admin/user';

export interface Training {
  id: string;
  text: string;
  color: string;
  content?: string;
  timeRange: [Dayjs, Dayjs];
  className: string; // 工位名称用于显示
  classId: string; // 工位ID用于存储
  bookingBy: string; // 员工名称用于显示
  bookingAt: string; // 员工ID用于存储
  date: Dayjs;
  status?: 'WAITING' | 'USING' | 'FINISH' | 'UNUSED';
  participants?: number;
}

export interface CalendarDay {
  date: string;
  dayNumber: number;
  isOtherMonth: boolean;
  isToday: boolean;
  trainings: Training[];
  allTrainings: Training[];
  moreCount: number;
}

export interface CalendarWeek {
  weekIndex: number;
  days: CalendarDay[];
}

export interface TrainingForm {
  timeRange: [Dayjs, Dayjs] | null;
  className: string;
  bookingBy: string;
  date: Dayjs | null;
  content: string;
}

// 训练状态类型（直接使用API状态）
export type TrainingStatus = 'WAITING' | 'USING' | 'FINISH' | 'UNUSED';

// 星期配置
export const WEEK_DAYS = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];

// 月份配置
export const MONTHS = [
  { value: 1, label: '1月' },
  { value: 2, label: '2月' },
  { value: 3, label: '3月' },
  { value: 4, label: '4月' },
  { value: 5, label: '5月' },
  { value: 6, label: '6月' },
  { value: 7, label: '7月' },
  { value: 8, label: '8月' },
  { value: 9, label: '9月' },
  { value: 10, label: '10月' },
  { value: 11, label: '11月' },
  { value: 12, label: '12月' },
];

// 训练状态配置（直接使用API状态）
export const TRAINING_STATUS_CONFIG = {
  WAITING: {
    label: '已预定',
    description: '已预定，未到时间',
    cssClass: 'scheduled',
  },
  USING: {
    label: '使用中',
    description: '训练正在进行中',
    cssClass: 'ongoing',
  },
  FINISH: {
    label: '已完成',
    description: '训练已完成',
    cssClass: 'completed',
  },
  UNUSED: {
    label: '已过期',
    description: '时间过，并且未使用',
    cssClass: 'expired',
  },
} as const;

// 日历配置
export const CALENDAR_CONFIG = {
  maxDisplayItems: 4, // 每日最多显示的训练项目数
  yearRange: 6, // 年份选择器范围（从当前年开始）
};

// 表单验证配置
export const FORM_CONFIG = {
  requiredFields: ['timeRange', 'className', 'bookingBy', 'date'],
  messages: {
    required: '请填写所有必填项',
    createSuccess: '训练计划创建成功',
    deleteSuccess: '训练计划删除成功',
    editInDevelopment: '编辑功能开发中',
  },
};

// 模拟数据配置
export const MOCK_DATA_CONFIG = {
  trainingProbability: 0.7, // 每日有训练的概率
  maxTrainingsPerDay: 3, // 每日最多训练数
  minTrainingsPerDay: 1, // 每日最少训练数
  trainingDurationMinutes: 30, // 训练时长（分钟）
  workingHours: {
    start: 8, // 工作开始时间
    end: 17, // 工作结束时间
  },
  defaultInstructors: ['张磊', '李华', '王强', '赵敏'],
  defaultClasses: ['A班门', 'B班门', 'C班门', 'D班门'],
  maxParticipants: 20, // 最大参与人数
};

// 表单配置
export const FORM_SCHEMAS = {
  // 创建训练计划表单
  createTraining: [
    {
      field: 'date',
      label: '预定日期',
      component: 'DatePicker',
      required: true,
      componentProps: {
        style: { width: '100%' },
        disabledDate: (current: any) => {
          if (!current) return false;

          const today = dayjs();
          // 获取当周的开始日期（周一）和结束日期（周日）
          const startOfWeek = today.startOf('week').add(1, 'day'); // 周一
          const endOfWeek = today.endOf('week').add(1, 'day'); // 周日

          // 禁用当周之外的日期
          if (current.isBefore(startOfWeek, 'day') || current.isAfter(endOfWeek, 'day')) {
            return true;
          }

          // 禁用今天之前的日期（当周内已经过去的日期）
          if (current.isBefore(today, 'day')) {
            return true;
          }

          return false;
        },
        placeholder: '请选择预定日期',
      },
      colProps: { span: 24 },
    },
    {
      field: 'timeRange',
      label: '预定时间',
      component: 'RangePicker',
      required: true,
      componentProps: {
        format: 'HH:mm',
        picker: 'time',
        style: { width: '100%' },
      },
      colProps: { span: 24 },
    },
    {
      field: 'classId',
      label: '预定工位',
      component: 'ApiTreeSelect',
      required: true,
      componentProps: {
        api: async () => {
          const res = await unitTree();
          // 筛选只有type为STATION能选择
          const filterStations = (nodes: any[]): any[] => {
            return nodes.filter((node) => {
              if (node.type !== 'STATION') {
                node.disabled = true;
              }
              if (node.childNodes && node.childNodes.length) {
                node.childNodes = filterStations(node.childNodes);
                return node.childNodes.length > 0;
              }
              return true;
            });
          };

          const filteredData = filterStations(res);
          return filteredData;
        },
        fieldNames: {
          label: 'name',
          value: 'id',
          key: 'id',
          children: 'childNodes',
        },
        showArrow: true,
        multiple: false, // 单选
        treeNodeFilterProp: 'name',
        placeholder: '请选择工位',
        // 默认展开所有层级，用户体验更好
        treeDefaultExpandAll: true,
      },
      colProps: { span: 24 },
    },
    {
      field: 'instructorId',
      label: '培训员工',
      component: 'ApiSelect',
      required: true,
      componentProps: {
        api: async () => {
          const res = await userQuery({
            currentPage: 1,
            pageSize: 1000, // 获取足够多的用户
            data: {
              status: 1, // 只获取启用的用户
            },
          });

          // 转换数据格式
          return (
            res?.records?.map((user: any) => ({
              label: user.name || user.username,
              value: user.id,
              key: user.id,
            })) || []
          );
        },
        showSearch: true,
        filterOption: (input: string, option: any) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
        placeholder: '请选择培训员工',
      },
      colProps: { span: 24 },
    },
  ] as FormSchema[],

  // 编辑训练计划表单（预留）
  editTraining: [
    {
      field: 'timeRange',
      label: '训练时间',
      component: 'RangePicker',
      required: true,
      componentProps: {
        format: 'HH:mm',
        picker: 'time',
        style: { width: '100%' },
      },
      colProps: { span: 12 },
    },
    {
      field: 'className',
      label: '训练班级',
      component: 'Input',
      required: true,
      componentProps: {
        placeholder: '如：A班门',
      },
      colProps: { span: 12 },
    },
    {
      field: 'instructor',
      label: '负责人',
      component: 'Input',
      required: true,
      componentProps: {
        placeholder: '请输入负责人姓名',
      },
      colProps: { span: 12 },
    },
    {
      field: 'date',
      label: '训练日期',
      component: 'DatePicker',
      required: true,
      componentProps: {
        style: { width: '100%' },
        disabled: true, // 编辑时日期不可修改
      },
      colProps: { span: 12 },
    },
    {
      field: 'content',
      label: '训练内容',
      component: 'InputTextArea',
      componentProps: {
        rows: 3,
        placeholder: '请输入训练内容描述',
      },
      colProps: { span: 24 },
    },
    {
      field: 'participants',
      label: '参与人数',
      component: 'InputNumber',
      componentProps: {
        min: 0,
        max: 50,
        placeholder: '请输入参与人数',
        style: { width: '100%' },
      },
      colProps: { span: 12 },
    },
    {
      field: 'status',
      label: '训练状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择训练状态',
        options: [
          { label: '已预定', value: 'WAITING' },
          { label: '使用中', value: 'USING' },
          { label: '已完成', value: 'FINISH' },
          { label: '已过期', value: 'UNUSED' },
        ],
      },
      colProps: { span: 12 },
    },
  ] as FormSchema[],
};

// 获取年份选项
export const getYearOptions = () => {
  const currentYear = new Date().getFullYear();
  return Array.from({ length: CALENDAR_CONFIG.yearRange }, (_, i) => currentYear + i);
};

// 获取训练状态对应的CSS类名
export const getTrainingStatusClass = (status: TrainingStatus): string => {
  switch (status) {
    case 'WAITING':
      return 'scheduled'; // 灰色
    case 'USING':
      return 'ongoing'; // 蓝色
    case 'FINISH':
      return 'completed'; // 绿色
    case 'UNUSED':
      return 'expired'; // 红色
    default:
      return 'scheduled'; // 默认灰色
  }
};

// 获取状态显示配置
export const getStatusConfig = (status: TrainingStatus) => {
  const statusConfig = {
    WAITING: { text: '已预定', color: '#999' },
    USING: { text: '使用中', color: '#4080FF' },
    FINISH: { text: '已完成', color: '#00996B' },
    UNUSED: { text: '已过期', color: '#EC544D' },
  };
  return statusConfig[status] || statusConfig.WAITING;
};

// 训练详情表格列配置
export const detailColumns = [
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80,
  },
  {
    title: '预定时间',
    dataIndex: 'timeRange',
    key: 'timeRange',
    width: 120,
  },
  {
    title: '预定工位',
    dataIndex: 'className',
    key: 'className',
    width: 120,
  },
  {
    title: '预约时间',
    dataIndex: 'bookingAt',
    key: 'bookingAt',
    width: 120,
  },
  {
    title: '预约人',
    dataIndex: 'bookingBy',
    key: 'bookingBy',
    width: 120,
  },
  {
    title: '操作',
    key: 'action',
    dataIndex: 'action',
    width: 120,
  },
];

// 生成模拟训练数据
export const generateMockTrainings = (): Training[] => {
  const mockTrainings: Training[] = [
    // 预设示例数据
    {
      id: '1',
      text: '08:00-08:10 A班门 张磊',
      color: '',
      content: '基础门训练',
      timeRange: [dayjs('08:00', 'HH:mm'), dayjs('08:10', 'HH:mm')],
      className: 'A班门',
      classId: 'station-001',
      instructor: '张磊',
      instructorId: 'user-001',
      date: dayjs('2025-07-01'),
      status: 'FINISH',
      participants: 15,
    },
    {
      id: '2',
      text: '10:00-10:30 B班门 李华',
      color: '',
      content: '正在进行的训练',
      timeRange: [dayjs('10:00', 'HH:mm'), dayjs('10:30', 'HH:mm')],
      className: 'B班门',
      classId: 'station-002',
      instructor: '李华',
      instructorId: 'user-002',
      date: dayjs(),
      status: 'USING',
      participants: 8,
    },
    {
      id: '3',
      text: '15:00-15:30 C班门 王强',
      color: '',
      content: '未来的训练计划',
      timeRange: [dayjs('15:00', 'HH:mm'), dayjs('15:30', 'HH:mm')],
      className: 'C班门',
      classId: 'station-003',
      instructor: '王强',
      instructorId: 'user-003',
      date: dayjs().add(1, 'day'),
      status: 'WAITING',
      participants: 0,
    },
    {
      id: '4',
      text: '07:00-07:30 D班门 赵敏',
      color: '',
      content: '已过期未参加的训练',
      timeRange: [dayjs('07:00', 'HH:mm'), dayjs('07:30', 'HH:mm')],
      className: 'D班门',
      classId: 'station-004',
      instructor: '赵敏',
      instructorId: 'user-004',
      date: dayjs().subtract(1, 'day'),
      status: 'UNUSED',
      participants: 0,
    },
  ];

  // 生成随机训练数据
  for (let day = 1; day <= 31; day++) {
    const date = dayjs(`2025-07-${day.toString().padStart(2, '0')}`);
    if (date.isValid() && Math.random() > 1 - MOCK_DATA_CONFIG.trainingProbability) {
      const trainingCount =
        Math.floor(Math.random() * MOCK_DATA_CONFIG.maxTrainingsPerDay) +
        MOCK_DATA_CONFIG.minTrainingsPerDay;

      for (let i = 0; i < trainingCount; i++) {
        const startHour =
          MOCK_DATA_CONFIG.workingHours.start +
          Math.floor(
            Math.random() *
              (MOCK_DATA_CONFIG.workingHours.end - MOCK_DATA_CONFIG.workingHours.start)
          );
        const startTime = dayjs(`${startHour}:00`, 'HH:mm');
        const endTime = startTime.add(MOCK_DATA_CONFIG.trainingDurationMinutes, 'minute');

        const statuses: TrainingStatus[] = ['FINISH', 'USING', 'WAITING', 'UNUSED'];
        const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
        const participants =
          randomStatus === 'UNUSED'
            ? 0
            : Math.floor(Math.random() * MOCK_DATA_CONFIG.maxParticipants) + 1;

        const randomInstructor =
          MOCK_DATA_CONFIG.defaultInstructors[
            Math.floor(Math.random() * MOCK_DATA_CONFIG.defaultInstructors.length)
          ];
        const randomClass =
          MOCK_DATA_CONFIG.defaultClasses[
            Math.floor(Math.random() * MOCK_DATA_CONFIG.defaultClasses.length)
          ];

        mockTrainings.push({
          id: `${day}-${i}`,
          text: `${startTime.format('HH:mm')}-${endTime.format('HH:mm')} ${randomClass} ${randomInstructor}`,
          color: '',
          content: `第${i + 1}个训练项目`,
          timeRange: [startTime, endTime],
          className: randomClass,
          classId: `station-${Math.floor(Math.random() * 100) + 1}`,
          instructor: randomInstructor,
          instructorId: `user-${Math.floor(Math.random() * 100) + 1}`,
          date: date,
          status: randomStatus,
          participants: participants,
        });
      }
    }
  }

  return mockTrainings;
};
